import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/cart_models.dart';
import '../api/wholesaler_api.dart';
import '../utils/quantity_validation.dart';

/// Service for managing shopping cart with wholesaler constraints
class CartService extends ChangeNotifier {
  static const String _cartKey = 'shopping_cart';

  Cart? _cart;
  bool _isLoading = false;
  String? _error;

  // Getters
  Cart? get cart => _cart;
  bool get hasCart => _cart != null && _cart!.isNotEmpty;
  bool get isEmpty => _cart == null || _cart!.isEmpty;
  bool get isNotEmpty => !isEmpty;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Cart summary getters
  int get itemCount => _cart?.itemCount ?? 0;
  int get totalQuantity => _cart?.totalQuantity ?? 0;
  double get totalPrice => _cart?.totalPrice ?? 0.0;
  int? get currentWholesalerId => _cart?.wholesalerId;
  String? get currentWholesalerTitle => _cart?.wholesalerTitle;

  /// Initialize the cart service and load persisted cart
  Future<void> initialize() async {
    await _loadCartFromStorage();
  }

  /// Add item to cart with wholesaler validation
  Future<CartOperationResponse> addToCart({
    required int wholesalerItemId,
    required WholesalerInfo wholesalerInfo,
    int quantity = 1,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      // Check if this wholesaler conflicts with current cart
      if (_cart != null && _cart!.wholesalerId != wholesalerInfo.id) {
        return CartOperationResponse(
          result: CartOperationResult.wholesalerConflict,
          message: 'لا يمكن إضافة منتجات من تاجر مختلف. يجب إفراغ السلة أولاً.',
          conflictingWholesaler: wholesalerInfo,
        );
      }

      // Get item details from API
      final itemResponse =
          await WholesalerApiService.getWholesalerItemById(wholesalerItemId);

      // Validate quantity constraints
      final quantityValidation = QuantityValidation.validateQuantity(
        quantity: quantity,
        minimumOrderQuantity: itemResponse.minimumOrderQuantity,
        maximumOrderQuantity: itemResponse.maximumOrderQuantity,
        productName: itemResponse.productName,
      );

      if (!quantityValidation.isValid) {
        return CartOperationResponse(
          result: CartOperationResult.quantityConstraintViolation,
          message: quantityValidation.errorMessage!,
        );
      }

      // Check inventory
      if (itemResponse.inventoryCount < quantity) {
        return const CartOperationResponse(
          result: CartOperationResult.insufficientInventory,
          message: 'الكمية المطلوبة غير متوفرة في المخزون.',
        );
      }

      // Create cart item
      final cartItem = CartItem(
        itemId: itemResponse.id,
        productName: itemResponse.productName,
        price: itemResponse.price,
        quantity: quantity,
        imageUrl: itemResponse.imageUrl,
        unit: itemResponse.unit,
        unitCount: itemResponse.unitCount,
        wholesalerItemId: wholesalerItemId,
        minimumOrderQuantity: itemResponse.minimumOrderQuantity,
        maximumOrderQuantity: itemResponse.maximumOrderQuantity,
        addedAt: DateTime.now(),
      );

      // Create or update cart
      if (_cart == null) {
        _cart = Cart(
          wholesalerId: wholesalerInfo.id,
          wholesalerTitle: wholesalerInfo.title,
          wholesalerLogo: wholesalerInfo.logoUrl,
          items: [cartItem],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      } else {
        // Check if item already exists
        final existingItemIndex = _cart!.items
            .indexWhere((item) => item.wholesalerItemId == wholesalerItemId);

        List<CartItem> updatedItems;
        if (existingItemIndex != -1) {
          // Update existing item quantity
          updatedItems = List.from(_cart!.items);
          updatedItems[existingItemIndex] = updatedItems[existingItemIndex]
              .copyWith(
                  quantity:
                      updatedItems[existingItemIndex].quantity + quantity);
        } else {
          // Add new item
          updatedItems = [..._cart!.items, cartItem];
        }

        _cart = _cart!.copyWith(items: updatedItems);
      }

      await _saveCartToStorage();
      notifyListeners();

      return const CartOperationResponse(result: CartOperationResult.success);
    } catch (e) {
      _setError('حدث خطأ أثناء إضافة المنتج إلى السلة: ${e.toString()}');
      return CartOperationResponse(
        result: CartOperationResult.error,
        message: 'حدث خطأ أثناء إضافة المنتج إلى السلة.',
      );
    } finally {
      _setLoading(false);
    }
  }

  /// Remove item from cart
  Future<CartOperationResponse> removeFromCart(int wholesalerItemId) async {
    if (_cart == null) {
      return const CartOperationResponse(
        result: CartOperationResult.itemNotFound,
        message: 'السلة فارغة.',
      );
    }

    try {
      _setLoading(true);
      _clearError();

      final updatedItems = _cart!.items
          .where((item) => item.wholesalerItemId != wholesalerItemId)
          .toList();

      if (updatedItems.isEmpty) {
        // Clear entire cart if no items left
        await clearCart();
      } else {
        _cart = _cart!.copyWith(items: updatedItems);
        await _saveCartToStorage();
        notifyListeners();
      }

      return const CartOperationResponse(result: CartOperationResult.success);
    } catch (e) {
      _setError('حدث خطأ أثناء حذف المنتج من السلة: ${e.toString()}');
      return CartOperationResponse(
        result: CartOperationResult.error,
        message: 'حدث خطأ أثناء حذف المنتج من السلة.',
      );
    } finally {
      _setLoading(false);
    }
  }

  /// Update item quantity in cart
  Future<CartOperationResponse> updateQuantity(
      int wholesalerItemId, int newQuantity) async {
    if (_cart == null) {
      return const CartOperationResponse(
        result: CartOperationResult.itemNotFound,
        message: 'السلة فارغة.',
      );
    }

    if (newQuantity <= 0) {
      return await removeFromCart(wholesalerItemId);
    }

    try {
      _setLoading(true);
      _clearError();

      // Verify inventory and quantity constraints for new quantity
      final itemResponse =
          await WholesalerApiService.getWholesalerItemById(wholesalerItemId);

      // Validate quantity constraints
      final quantityValidation = QuantityValidation.validateQuantity(
        quantity: newQuantity,
        minimumOrderQuantity: itemResponse.minimumOrderQuantity,
        maximumOrderQuantity: itemResponse.maximumOrderQuantity,
        productName: itemResponse.productName,
      );

      if (!quantityValidation.isValid) {
        return CartOperationResponse(
          result: CartOperationResult.quantityConstraintViolation,
          message: quantityValidation.errorMessage!,
        );
      }

      if (itemResponse.inventoryCount < newQuantity) {
        return const CartOperationResponse(
          result: CartOperationResult.insufficientInventory,
          message: 'الكمية المطلوبة غير متوفرة في المخزون.',
        );
      }

      final itemIndex = _cart!.items
          .indexWhere((item) => item.wholesalerItemId == wholesalerItemId);

      if (itemIndex == -1) {
        return const CartOperationResponse(
          result: CartOperationResult.itemNotFound,
          message: 'المنتج غير موجود في السلة.',
        );
      }

      final updatedItems = List<CartItem>.from(_cart!.items);
      updatedItems[itemIndex] =
          updatedItems[itemIndex].copyWith(quantity: newQuantity);

      _cart = _cart!.copyWith(items: updatedItems);
      await _saveCartToStorage();
      notifyListeners();

      return const CartOperationResponse(result: CartOperationResult.success);
    } catch (e) {
      _setError('حدث خطأ أثناء تحديث كمية المنتج: ${e.toString()}');
      return CartOperationResponse(
        result: CartOperationResult.error,
        message: 'حدث خطأ أثناء تحديث كمية المنتج.',
      );
    } finally {
      _setLoading(false);
    }
  }

  /// Clear entire cart
  Future<void> clearCart() async {
    try {
      _cart = null;
      await _clearCartFromStorage();
      notifyListeners();
    } catch (e) {
      _setError('حدث خطأ أثناء إفراغ السلة: ${e.toString()}');
    }
  }

  /// Switch to a different wholesaler (clears current cart)
  Future<CartOperationResponse> switchWholesaler(
      WholesalerInfo newWholesaler) async {
    try {
      await clearCart();
      return const CartOperationResponse(result: CartOperationResult.success);
    } catch (e) {
      return CartOperationResponse(
        result: CartOperationResult.error,
        message: 'حدث خطأ أثناء تغيير التاجر.',
      );
    }
  }

  /// Get cart item by wholesaler item ID
  CartItem? getCartItem(int wholesalerItemId) {
    if (_cart == null) return null;
    try {
      return _cart!.items
          .firstWhere((item) => item.wholesalerItemId == wholesalerItemId);
    } catch (e) {
      return null;
    }
  }

  /// Check if product is in cart
  bool isInCart(int wholesalerItemId) {
    return getCartItem(wholesalerItemId) != null;
  }

  /// Get quantity of specific item in cart
  int getItemQuantity(int wholesalerItemId) {
    final item = getCartItem(wholesalerItemId);
    return item?.quantity ?? 0;
  }

  /// Check if quantity can be incremented for a cart item
  bool canIncrementQuantity(int wholesalerItemId) {
    final item = getCartItem(wholesalerItemId);
    if (item == null) return false;

    return !QuantityValidation.shouldDisableIncrement(
      currentQuantity: item.quantity,
      maximumOrderQuantity: item.maximumOrderQuantity,
    );
  }

  /// Check if quantity can be decremented for a cart item
  bool canDecrementQuantity(int wholesalerItemId) {
    final item = getCartItem(wholesalerItemId);
    if (item == null) return false;

    return !QuantityValidation.shouldDisableDecrement(
      currentQuantity: item.quantity,
      minimumOrderQuantity: item.minimumOrderQuantity,
    );
  }

  /// Get quantity range text for display
  String getQuantityRangeText(int wholesalerItemId) {
    final item = getCartItem(wholesalerItemId);
    if (item == null) return '';

    return QuantityValidation.getQuantityRangeText(
      minimumOrderQuantity: item.minimumOrderQuantity,
      maximumOrderQuantity: item.maximumOrderQuantity,
    );
  }

  /// Validate all cart items before order creation
  Future<CartValidationResult> validateCartForOrder() async {
    if (_cart == null || _cart!.isEmpty) {
      return const CartValidationResult(
        isValid: false,
        errorMessage: 'السلة فارغة',
        invalidItems: [],
      );
    }

    final invalidItems = <CartValidationError>[];

    for (final cartItem in _cart!.items) {
      try {
        // Get latest item details to check current constraints and inventory
        final itemResponse = await WholesalerApiService.getWholesalerItemById(
            cartItem.wholesalerItemId);

        // Validate quantity constraints
        final quantityValidation = QuantityValidation.validateQuantity(
          quantity: cartItem.quantity,
          minimumOrderQuantity: itemResponse.minimumOrderQuantity,
          maximumOrderQuantity: itemResponse.maximumOrderQuantity,
          productName: cartItem.productName,
        );

        if (!quantityValidation.isValid) {
          invalidItems.add(CartValidationError(
            cartItem: cartItem,
            errorType: CartValidationErrorType.quantityConstraint,
            errorMessage: quantityValidation.errorMessage!,
          ));
          continue;
        }

        // Check inventory
        if (itemResponse.inventoryCount < cartItem.quantity) {
          invalidItems.add(CartValidationError(
            cartItem: cartItem,
            errorType: CartValidationErrorType.insufficientInventory,
            errorMessage:
                'الكمية المطلوبة (${cartItem.quantity}) غير متوفرة في المخزون. المتوفر: ${itemResponse.inventoryCount}',
          ));
        }
      } catch (e) {
        // If item no longer exists or API error
        invalidItems.add(CartValidationError(
          cartItem: cartItem,
          errorType: CartValidationErrorType.itemNotFound,
          errorMessage: 'المنتج غير متوفر حالياً',
        ));
      }
    }

    if (invalidItems.isNotEmpty) {
      return CartValidationResult(
        isValid: false,
        errorMessage: 'توجد مشاكل في بعض المنتجات في السلة',
        invalidItems: invalidItems,
      );
    }

    return const CartValidationResult(
      isValid: true,
      errorMessage: null,
      invalidItems: [],
    );
  }

  /// Validate cart items against current inventory
  Future<List<CartItem>> validateCartInventory() async {
    if (_cart == null || _cart!.isEmpty) return [];

    final invalidItems = <CartItem>[];

    for (final cartItem in _cart!.items) {
      try {
        final itemResponse = await WholesalerApiService.getWholesalerItemById(
            cartItem.wholesalerItemId);

        if (itemResponse.inventoryCount < cartItem.quantity) {
          invalidItems.add(cartItem);
        }
      } catch (e) {
        // If item no longer exists, consider it invalid
        invalidItems.add(cartItem);
      }
    }

    return invalidItems;
  }

  /// Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Load cart from SharedPreferences
  Future<void> _loadCartFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartJson = prefs.getString(_cartKey);

      if (cartJson != null) {
        final cartData = json.decode(cartJson);
        _cart = Cart.fromJson(cartData);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading cart from storage: $e');
      // If there's an error loading, clear corrupted data
      await _clearCartFromStorage();
    }
  }

  /// Save cart to SharedPreferences
  Future<void> _saveCartToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_cart != null) {
        final cartJson = json.encode(_cart!.toJson());
        await prefs.setString(_cartKey, cartJson);
      } else {
        await prefs.remove(_cartKey);
      }
    } catch (e) {
      debugPrint('Error saving cart to storage: $e');
    }
  }

  /// Clear cart from SharedPreferences
  Future<void> _clearCartFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cartKey);
    } catch (e) {
      debugPrint('Error clearing cart from storage: $e');
    }
  }

  /// Debug method to print cart state
  void debugPrintCart() {
    if (kDebugMode) {
      print('=== CART DEBUG ===');
      if (_cart == null) {
        print('Cart is null');
      } else {
        print(
            'Wholesaler: ${_cart!.wholesalerTitle} (ID: ${_cart!.wholesalerId})');
        print('Items: ${_cart!.itemCount}');
        print('Total: \$${_cart!.totalPrice.toStringAsFixed(2)}');
        for (final item in _cart!.items) {
          print(
              '  - ${item.productName} x${item.quantity} = \$${item.totalPrice.toStringAsFixed(2)}');
        }
      }
      print('==================');
    }
  }
}
